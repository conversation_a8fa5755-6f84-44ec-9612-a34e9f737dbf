// scripts/show-env.js
// Run this with: node scripts/show-env.js or npm run show-env

const { loadEnvConfig } = require('@next/env');
const path = require('path');

// Load environment variables the same way Next.js does
const projectDir = process.cwd();
loadEnvConfig(projectDir);

console.log('='.repeat(60));
console.log('ENVIRONMENT VARIABLES FROM .env.local');
console.log('='.repeat(60));

const envVars = [
  'NEXT_PUBLIC_WORDPRESS_URL',
  'WOOCOMMERCE_GRAPHQL_URL',
  'WOOCOMMERCE_CONSUMER_KEY',
  'WOOCOMMERCE_CONSUMER_SECRET',
  'WOOCOMMERCE_JWT_SECRET',
  'WOOCOMMERCE_REVALIDATION_SECRET',
  'UPSTASH_REDIS_REST_URL',
  'UPSTASH_REDIS_REST_TOKEN',
  'QSTASH_TOKEN',
  'NEXT_PUBLIC_COMMERCE_PROVIDER',
  'NEXT_PUBLIC_BACKEND_URL',
  'NEXT_PUBLIC_WOOCOMMERCE_URL'
];

console.log('\n📋 PUBLIC VARIABLES (NEXT_PUBLIC_*):');
console.log('-'.repeat(40));
envVars
  .filter(name => name.startsWith('NEXT_PUBLIC_'))
  .forEach(name => {
    const value = process.env[name];
    console.log(`${name}: ${value || '❌ UNDEFINED'}`);
  });

console.log('\n🔒 PRIVATE VARIABLES (Server-only):');
console.log('-'.repeat(40));
envVars
  .filter(name => !name.startsWith('NEXT_PUBLIC_'))
  .forEach(name => {
    const value = process.env[name];
    if (value) {
      // Show partial value for security
      if (name.includes('SECRET') || name.includes('TOKEN') || name.includes('KEY')) {
        console.log(`${name}: ${value.substring(0, 10)}... (${value.length} chars total)`);
      } else {
        console.log(`${name}: ${value}`);
      }
    } else {
      console.log(`${name}: ❌ UNDEFINED`);
    }
  });

console.log('\n📊 SUMMARY:');
console.log('-'.repeat(40));
const definedVars = envVars.filter(name => process.env[name]);
const undefinedVars = envVars.filter(name => !process.env[name]);

console.log(`✅ Loaded: ${definedVars.length}/${envVars.length} variables`);
console.log(`❌ Missing: ${undefinedVars.length} variables`);

if (undefinedVars.length > 0) {
  console.log(`\n⚠️  Missing variables: ${undefinedVars.join(', ')}`);
}

console.log('\n💡 USAGE NOTES:');
console.log('-'.repeat(40));
console.log('• NEXT_PUBLIC_* variables are available in browser');
console.log('• Other variables are only available on server-side');
console.log('• Check your .env.local file if any variables are missing');
console.log('• Restart your dev server after changing .env.local');

console.log('\n🔍 ENVIRONMENT INFO:');
console.log('-'.repeat(40));
console.log(`NODE_ENV: ${process.env.NODE_ENV || 'undefined'}`);
console.log(`Working Directory: ${projectDir}`);
console.log(`Env File Path: ${path.join(projectDir, 'ankkor/.env.local')}`);
